-- Migration: Add partial invoicing support to invoices table
-- This migration adds columns to support partial invoicing functionality

-- Add columns for partial invoicing
ALTER TABLE invoices 
ADD COLUMN IF NOT EXISTS partial_amount DECIMAL(10,2) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS amount_paid DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_partial BOOLEAN DEFAULT FALSE;

-- Add comments for documentation
COMMENT ON COLUMN invoices.partial_amount IS 'The partial amount to invoice (if different from total amount)';
COMMENT ON COLUMN invoices.amount_paid IS 'The total amount paid so far on this invoice';
COMMENT ON COLUMN invoices.is_partial IS 'Whether this invoice is for a partial amount of the total';

-- Update existing invoices to have amount_paid = 0 and is_partial = false
UPDATE invoices 
SET amount_paid = 0, is_partial = false 
WHERE amount_paid IS NULL OR is_partial IS NULL;
