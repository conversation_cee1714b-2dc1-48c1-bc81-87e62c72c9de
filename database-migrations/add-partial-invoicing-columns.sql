-- Migration: Add partial invoicing support to invoices table
-- This migration adds columns to support partial invoicing functionality

-- Add columns for partial invoicing
ALTER TABLE invoices
ADD COLUMN IF NOT EXISTS partial_amount DECIMAL(10,2) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS amount_paid DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_partial BOOLEAN DEFAULT FALSE;

-- Add comments for documentation
COMMENT ON COLUMN invoices.partial_amount IS 'The partial amount to invoice (if different from total amount)';
COMMENT ON COLUMN invoices.amount_paid IS 'The total amount paid so far on this invoice';
COMMENT ON COLUMN invoices.is_partial IS 'Whether this invoice is for a partial amount of the total';

-- Update existing invoices to have amount_paid = 0 and is_partial = false
UPDATE invoices
SET amount_paid = 0, is_partial = false
WHERE amount_paid IS NULL OR is_partial IS NULL;

-- Update the create_invoice stored procedure to handle new columns
-- Note: This assumes the procedure exists and needs to be updated
-- You may need to modify this based on your actual stored procedure implementation

CREATE OR REPLACE FUNCTION create_invoice(
  p_customer_id UUID,
  p_project_id UUID DEFAULT NULL,
  p_quote_id UUID DEFAULT NULL,
  p_title TEXT,
  p_status TEXT,
  p_due_date DATE,
  p_po_number TEXT DEFAULT NULL,
  p_parent_invoice_id UUID DEFAULT NULL,
  p_partial_amount DECIMAL(10,2) DEFAULT NULL,
  p_amount_paid DECIMAL(10,2) DEFAULT 0,
  p_is_partial BOOLEAN DEFAULT FALSE,
  p_items JSONB
) RETURNS UUID AS $$
DECLARE
  invoice_id UUID;
  total_amount DECIMAL(10,2) := 0;
  item JSONB;
BEGIN
  -- Calculate total amount from items
  FOR item IN SELECT * FROM jsonb_array_elements(p_items)
  LOOP
    total_amount := total_amount + (item->>'total')::DECIMAL(10,2);
  END LOOP;

  -- Insert the invoice
  INSERT INTO invoices (
    customer_id, project_id, quote_id, title, amount,
    partial_amount, amount_paid, is_partial, status,
    due_date, po_number, parent_invoice_id
  ) VALUES (
    p_customer_id, p_project_id, p_quote_id, p_title, total_amount,
    p_partial_amount, p_amount_paid, p_is_partial, p_status,
    p_due_date, p_po_number, p_parent_invoice_id
  ) RETURNING id INTO invoice_id;

  -- Insert invoice items
  FOR item IN SELECT * FROM jsonb_array_elements(p_items)
  LOOP
    INSERT INTO invoice_items (
      invoice_id, description, quantity, unit_price, total
    ) VALUES (
      invoice_id,
      item->>'description',
      (item->>'quantity')::INTEGER,
      (item->>'unit_price')::DECIMAL(10,2),
      (item->>'total')::DECIMAL(10,2)
    );
  END LOOP;

  RETURN invoice_id;
END;
$$ LANGUAGE plpgsql;
