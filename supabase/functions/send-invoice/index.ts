// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.com/manual/getting_started/javascript

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { corsHeaders } from "../_shared/cors.ts"

const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')

interface EmailRequest {
  invoice: {
    id: string;
    number: string;
    title: string;
    amount: number;
    dueDate: string;
    poNumber?: string;
    status: string;
  };
  customer: {
    name: string;
    email: string;
    contactName: string;
  };
  pdfBase64: string;
  message: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the request body
    const { invoice, customer, pdfBase64, message } = await req.json() as EmailRequest

    // Validate required fields
    if (!invoice || !customer || !pdfBase64) {
      return new Response(
        JSON.stringify({ success: false, message: 'Missing required fields' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    if (!RESEND_API_KEY) {
      return new Response(
        JSON.stringify({ success: false, message: 'Resend API key not configured' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    // Format the amount as currency
    const formattedAmount = new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(invoice.amount)

    // Format the date
    const formattedDate = new Date(invoice.dueDate).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })

    // Create email content
    const emailContent = `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #0f172a; border-bottom: 1px solid #eee; padding-bottom: 10px;">Invoice from Tech Local</h1>
            
            <p>Dear ${customer.contactName || customer.name},</p>
            
            <p>Please find attached your invoice <strong>${invoice.title}</strong> (${invoice.number}) for the amount of <strong>${formattedAmount}</strong>.</p>

            ${invoice.poNumber ? `<p>Purchase Order: <strong>${invoice.poNumber}</strong></p>` : ''}

            <p>Due date: <strong>${formattedDate}</strong></p>
            
            <p>If you have any questions about this invoice, please don't hesitate to contact us.</p>
            
            <p>Thank you for your business!</p>
            
            <p style="margin-top: 30px;">
              <strong>Tech Local (Pty) Ltd</strong><br>
              18 Rosyth Road<br>
              East London<br>
              South Africa<br>
              <EMAIL><br>
              https://techlocal.co.za
            </p>
          </div>
        </body>
      </html>
    `

    // Send email using Resend
    const resendResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        from: 'Tech Local <<EMAIL>>',
        to: [customer.email],
        bcc: ['<EMAIL>'],
        subject: `Invoice ${invoice.number} from Tech Local`,
        html: emailContent,
        attachments: [
          {
            filename: `Invoice-${invoice.number}.pdf`,
            content: pdfBase64
          }
        ]
      })
    })

    const resendData = await resendResponse.json()

    if (resendResponse.ok) {
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: `Invoice has been sent to ${customer.email}` 
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } else {
      console.error('Resend API error:', resendData)
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: `Failed to send email: ${resendData.message || 'Unknown error'}` 
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }
  } catch (error) {
    console.error('Error sending invoice email:', error)
    return new Response(
      JSON.stringify({ success: false, message: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
