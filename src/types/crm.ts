// CRM Types

export type Customer = {
  id: string;
  name: string;
  contactName: string;
  contactNumber: string;
  email: string;
  address?: string;
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'inactive' | 'lead';
  notes?: string;
};

export type Deal = {
  id: string;
  customerId: string;
  title: string;
  value: number;
  stage: 'lead' | 'qualified' | 'proposal' | 'negotiation' | 'closed-won' | 'closed-lost';
  probability: number;
  expectedCloseDate?: string;
  createdAt: string;
  updatedAt: string;
  notes?: string;
  assignedTo?: string;
};

export type Project = {
  id: string;
  customerId: string;
  dealId?: string;
  title: string;
  description: string;
  status: 'planning' | 'in-progress' | 'review' | 'completed' | 'on-hold';
  startDate?: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
  budget?: number;
  currentWebsite?: string;
  goals: string;
};

export type Quote = {
  id: string;
  customerId: string;
  dealId?: string;
  projectId?: string;
  title: string;
  amount: number;
  status: 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired' | 'archived';
  validUntil: string;
  createdAt: string;
  updatedAt: string;
  items: QuoteItem[];
  parentQuoteId?: string; // References the original quote that this quote is a revision of
};

export type QuoteItem = {
  id?: string;  // Optional for new items
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
};

export type Invoice = {
  id: string;
  customerId: string;
  projectId?: string;
  quoteId?: string;
  poNumber?: string;
  title: string;
  amount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled' | 'archived';
  dueDate: string;
  createdAt: string;
  updatedAt: string;
  items: InvoiceItem[];
  parentInvoiceId?: string; // References the original invoice that this invoice is a revision of
};

// Helper function to get invoice number from ID
export const getInvoiceNumber = (id: string): string => {
  return id.substring(0, 6).toUpperCase();
};

export type InvoiceItem = {
  id?: string;  // Optional for new items
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
};

export type Activity = {
  id: string;
  type: 'note' | 'email' | 'call' | 'meeting' | 'task';
  title: string;
  description?: string;
  relatedTo: {
    type: 'customer' | 'deal' | 'project' | 'quote' | 'invoice';
    id: string;
  };
  createdAt: string;
  createdBy: string;
  dueDate?: string;
  completed?: boolean;
};
