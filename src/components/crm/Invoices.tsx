import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Download,
  Mail,
  CheckCircle2,
  Co<PERSON>,
  Copy<PERSON><PERSON>,
  Al<PERSON><PERSON><PERSON>gle,
  Eye
} from "lucide-react";
import CrmLayout from "./CrmLayout";
import { customerService, projectService, quoteService, invoiceService } from "@/services";
import { invoiceUtilsService } from "@/services/invoiceUtilsService";
import InvoicePreviewDialog from "./InvoicePreviewDialog";
import CopyLineItemsDialog, { LineItemAction } from "./CopyLineItemsDialog";
import StatusChangeDropdown from "./StatusChangeDropdown";
import { Invoice, Customer, Project, Quote, getInvoiceNumber } from "@/types/crm";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
// import { supabase } from "@/integrations/supabase/client";
import { formatCurrency } from "@/lib/utils";

const invoiceItemSchema = z.object({
  id: z.string().optional(),
  description: z.string().min(1, "Description is required"),
  quantity: z.coerce.number().min(1, "Quantity must be at least 1"),
  unitPrice: z.coerce.number().min(0, "Unit price must be at least 0"),
  total: z.coerce.number().min(0, "Total must be at least 0"),
});

const invoiceFormSchema = z.object({
  customerId: z.string().min(1, "Customer is required"),
  projectId: z.string().optional(),
  quoteId: z.string().optional(),
  poNumber: z.string().optional(),
  title: z.string().min(1, "Title is required"),
  status: z.enum(["draft", "sent", "paid", "overdue", "cancelled", "archived"]),
  dueDate: z.string().min(1, "Due date is required"),
  items: z.array(invoiceItemSchema).min(1, "At least one item is required"),
});

type InvoiceFormValues = z.infer<typeof invoiceFormSchema>;

const Invoices = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isReviseDialogOpen, setIsReviseDialogOpen] = useState(false);

  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [activePreviewTab, setActivePreviewTab] = useState<"preview" | "download" | "email">("preview");
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isCopyLineItemsDialogOpen, setIsCopyLineItemsDialogOpen] = useState(false);
  const [selectedQuoteForCopy, setSelectedQuoteForCopy] = useState<Quote | null>(null);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [highlightedInvoiceId, setHighlightedInvoiceId] = useState<string | null>(null);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Effect to scroll to highlighted invoice and clear highlight after a delay
  useEffect(() => {
    if (highlightedInvoiceId) {
      // Use setTimeout to ensure the DOM has updated
      setTimeout(() => {
        const highlightedRow = document.getElementById(`invoice-row-${highlightedInvoiceId}`);
        if (highlightedRow) {
          highlightedRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        // Clear the highlight after 3 seconds
        const clearHighlightTimer = setTimeout(() => {
          setHighlightedInvoiceId(null);
        }, 3000);

        // Clean up the timer if the component unmounts or highlightedInvoiceId changes
        return () => clearTimeout(clearHighlightTimer);
      }, 500);
    }
  }, [highlightedInvoiceId]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch data in parallel
        const [invoicesData, customersData, projectsData, quotesData] = await Promise.all([
          invoiceService.getInvoices(),
          customerService.getCustomers(),
          projectService.getProjects(),
          quoteService.getQuotes()
        ]);

        setInvoices(invoicesData);
        setCustomers(customersData);
        setProjects(projectsData);
        setQuotes(quotesData);

        // Check for query parameters
        const urlParams = new URLSearchParams(window.location.search);
        const editInvoiceId = urlParams.get('edit');
        const highlightId = urlParams.get('highlight');

        if (editInvoiceId) {
          // Find the invoice to edit
          const invoiceToEdit = invoicesData.find(inv => inv.id === editInvoiceId);
          if (invoiceToEdit) {
            // Open the edit dialog for this invoice
            handleEditInvoice(invoiceToEdit);

            // Remove the query parameter from the URL without refreshing the page
            const newUrl = window.location.pathname;
            window.history.pushState({ path: newUrl }, '', newUrl);
          }
        } else if (highlightId) {
          // Set the highlighted invoice ID
          setHighlightedInvoiceId(highlightId);

          // Find the invoice to highlight
          const invoiceToHighlight = invoicesData.find(inv => inv.id === highlightId);
          if (invoiceToHighlight) {
            // Show a toast to indicate the highlighted invoice
            toast({
              title: "Invoice Found",
              description: `Invoice #${invoiceToHighlight.id.substring(0, 8).toUpperCase()} is highlighted below.`,
            });

            // Remove the query parameter from the URL without refreshing the page
            const newUrl = window.location.pathname;
            window.history.pushState({ path: newUrl }, '', newUrl);
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load invoices data',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  const form = useForm<InvoiceFormValues>({
    resolver: zodResolver(invoiceFormSchema),
    defaultValues: {
      customerId: "",
      projectId: "none",
      quoteId: "none",
      poNumber: "",

      status: "draft",
      dueDate: "",
      items: [
        {
          description: "",
          quantity: 1,
          unitPrice: 0,
          total: 0,
        }
      ],
    }
  });

  const filteredInvoices = invoices.filter(invoice => {
    const invoiceNumber = getInvoiceNumber(invoice.id);
    const matchesSearch =
      invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.title.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || invoice.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Get title from quote or project
  const getTitleFromRelatedEntity = (customerId: string, quoteId: string | undefined, projectId: string | undefined) => {
    if (quoteId && quoteId !== 'none') {
      const quote = quotes.find(q => q.id === quoteId);
      if (quote) return `Invoice for ${quote.title}`;
    }

    if (projectId && projectId !== 'none') {
      const project = projects.find(p => p.id === projectId);
      if (project) return `Invoice for ${project.title}`;
    }

    const customer = customers.find(c => c.id === customerId);
    return customer ? `Invoice for ${customer.name}` : 'New Invoice';
  };

  // Handle when a quote is selected for an existing invoice
  const handleQuoteSelection = (quoteId: string) => {
    // Find the selected quote
    const selectedQuote = quotes.find(q => q.id === quoteId);
    if (selectedQuote) {
      // Set the selected quote for copying line items
      setSelectedQuoteForCopy(selectedQuote);
      setIsCopyLineItemsDialogOpen(true);
    }
  };

  // Handler for previewing an invoice
  const handlePreviewInvoice = (invoice: Invoice, initialTab: "preview" | "download" | "email" = "preview") => {
    setActivePreviewTab(initialTab);
    setSelectedInvoice(invoice);
    const customer = customers.find(c => c.id === invoice.customerId);
    if (customer) {
      setSelectedCustomer(customer);
      setIsPreviewDialogOpen(true);
      // Close the edit dialog if it's open
      setIsAddDialogOpen(false);
    } else {
      toast({
        title: "Error",
        description: "Customer information not found.",
        variant: "destructive",
      });
    }
  };

  // Handler for directly downloading an invoice PDF without opening the preview dialog
  const handleDownloadInvoice = async (invoice: Invoice) => {
    try {
      // Find the customer for this invoice
      const customer = customers.find(c => c.id === invoice.customerId);
      if (!customer) {
        toast({
          title: "Error",
          description: "Customer information not found.",
          variant: "destructive",
        });
        return;
      }

      // Show loading toast
      toast({
        title: "Generating PDF",
        description: "Please wait while we generate your PDF...",
      });

      // Generate the PDF directly
      const pdfBase64 = await invoiceUtilsService.generatePDF(document.createElement('div'), invoice, customer);

      // Download the PDF and handle status change if needed
      await invoiceUtilsService.downloadInvoicePDF(pdfBase64, invoice.id, invoice);

      // Refresh invoices list in case the status was changed
      const updatedInvoices = await invoiceService.getInvoices();
      setInvoices(updatedInvoices);

      // Show success toast
      toast({
        title: "Success",
        description: "PDF downloaded successfully.",
      });
    } catch (error) {
      console.error("Error downloading PDF:", error);
      toast({
        title: "Error",
        description: "Failed to generate PDF. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleAddInvoice = async (data: InvoiceFormValues) => {
    // Check if we're saving a non-draft status and show a warning
    if (data.status !== 'draft' && selectedInvoice?.parentInvoiceId) {
      // This is a revised invoice being saved with a non-draft status
      const confirmNonDraft = window.confirm(
        "You are saving this invoice with a non-draft status. Once saved, you won't be able to edit it further. Continue?"
      );

      if (!confirmNonDraft) {
        return; // User cancelled the operation
      }
    }

    // Prepare the data for submission
    const invoiceData: any = {
      ...data,
      // Convert 'none' to undefined for optional fields
      projectId: data.projectId === 'none' ? undefined : data.projectId,
      quoteId: data.quoteId === 'none' ? undefined : data.quoteId,
      poNumber: data.poNumber || undefined,
    };

    try {
      if (selectedInvoice) {
        // Check if we're changing status from draft to something else and there's an associated quote
        const isStatusChangingFromDraft = selectedInvoice.status === 'draft' && data.status !== 'draft';
        const hasAssociatedQuote = selectedInvoice.quoteId && selectedInvoice.quoteId !== 'none';

        // Update existing invoice
        await invoiceService.updateInvoice(selectedInvoice.id, invoiceData);

        // If status changed from draft to something else and there's an associated quote, update the quote status to accepted
        if (isStatusChangingFromDraft && hasAssociatedQuote) {
          try {
            await quoteService.updateQuote(selectedInvoice.quoteId as string, { status: 'accepted' });
            toast({
              title: "Quote Status Updated",
              description: "The associated quote has been marked as accepted.",
            });

            // Refresh quotes list
            const updatedQuotes = await quoteService.getQuotes();
            setQuotes(updatedQuotes);
          } catch (quoteError) {
            console.error("Error updating quote status:", quoteError);
            toast({
              title: "Warning",
              description: "Invoice updated but failed to update the associated quote status.",
              variant: "destructive",
            });
          }
        }

        toast({
          title: "Invoice Updated",
          description: `Invoice has been updated successfully.`,
        });
      } else {
        // Create new invoice
        await invoiceService.createInvoice(invoiceData);

        // If it's a sent invoice with an associated quote, update the quote status to accepted
        if (data.status === 'sent' && data.quoteId && data.quoteId !== 'none') {
          try {
            await quoteService.updateQuote(data.quoteId, { status: 'accepted' });
            toast({
              title: "Quote Status Updated",
              description: "The associated quote has been marked as accepted.",
            });

            // Refresh quotes list
            const updatedQuotes = await quoteService.getQuotes();
            setQuotes(updatedQuotes);
          } catch (quoteError) {
            console.error("Error updating quote status:", quoteError);
            toast({
              title: "Warning",
              description: "Invoice created but failed to update the associated quote status.",
              variant: "destructive",
            });
          }
        }

        toast({
          title: "Invoice Added",
          description: `Invoice has been added successfully.`,
        });
      }

      // Refresh invoice list
      const updatedInvoices = await invoiceService.getInvoices();
      setInvoices(updatedInvoices);

      setIsAddDialogOpen(false);
      form.reset();
    } catch (error) {
      console.error("Error saving invoice:", error);
      toast({
        title: "Error",
        description: "Failed to save invoice. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditInvoice = (invoice: Invoice) => {
    // Only allow direct editing of draft invoices
    if (invoice.status !== 'draft') {
      toast({
        title: "Cannot Edit",
        description: "Only draft invoices can be edited directly. Please use 'Revise Invoice' for non-draft invoices.",
        variant: "destructive",
      });
      return;
    }

    setSelectedInvoice(invoice);
    form.reset({
      customerId: invoice.customerId,
      projectId: invoice.projectId || "none",
      quoteId: invoice.quoteId || "none",
      poNumber: invoice.poNumber || "",
      title: invoice.title,
      status: invoice.status,
      dueDate: new Date(invoice.dueDate).toISOString().split('T')[0],
      items: invoice.items,
    });
    setIsAddDialogOpen(true);
  };

  const handleDeleteInvoice = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (selectedInvoice) {
      try {
        await invoiceService.deleteInvoice(selectedInvoice.id);
        toast({
          title: "Invoice Deleted",
          description: `Invoice ${getInvoiceNumber(selectedInvoice.id)} has been deleted.`,
        });

        // Refresh invoice list
        const updatedInvoices = await invoiceService.getInvoices();
        setInvoices(updatedInvoices);
      } catch (error) {
        console.error("Error deleting invoice:", error);
        toast({
          title: "Error",
          description: "Failed to delete invoice. Please try again.",
          variant: "destructive",
        });
      }
    }
    setIsDeleteDialogOpen(false);
    setSelectedInvoice(null);
  };

  const getCustomerName = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'Unknown Customer';
  };

  // Function to navigate to customer edit page
  const handleViewCustomer = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId);
    if (customer) {
      // Navigate to the customer page and open the edit modal
      window.location.href = `/admin/customers?edit=${customer.id}`;
    }
  };

  const getStatusColor = (status: Invoice['status']) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-amber-100 text-amber-800';
      case 'archived':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handler for duplicating an invoice
  const handleDuplicateInvoice = async (invoice: Invoice) => {
    try {
      // Show loading toast
      toast({
        title: "Duplicating Invoice",
        description: "Creating a copy of the invoice...",
      });

      // Prepare the invoice data
      const invoiceData: Omit<Invoice, "id" | "createdAt" | "updatedAt" | "amount"> = {
        customerId: invoice.customerId,
        projectId: invoice.projectId,
        quoteId: undefined, // Don't copy the quote reference
        poNumber: invoice.poNumber,
        title: `Copy of ${invoice.title}`,
        status: 'draft' as Invoice['status'], // Always start as draft
        dueDate: new Date().toISOString().split('T')[0], // Set to today
        items: invoice.items.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.quantity * item.unitPrice
        })),
        parentInvoiceId: undefined
      };

      // Create the new invoice directly
      const newInvoice = await invoiceService.createInvoice(invoiceData);

      // Refresh the invoices list
      const updatedInvoices = await invoiceService.getInvoices();
      setInvoices(updatedInvoices);

      // Highlight the new invoice
      setHighlightedInvoiceId(newInvoice.id);

      // Show success toast
      toast({
        title: "Invoice Duplicated",
        description: "A new draft invoice has been created.",
      });

    } catch (error) {
      console.error("Error duplicating invoice:", error);
      toast({
        title: "Error",
        description: "Failed to duplicate invoice. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handler for revising an invoice
  const handleReviseInvoice = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setIsReviseDialogOpen(true);
  };

  // Confirm invoice revision
  const confirmReviseInvoice = async () => {
    if (!selectedInvoice) return;

    try {
      // First, mark the original invoice as cancelled
      await invoiceService.updateInvoice(selectedInvoice.id, { status: 'cancelled' });

      // Create a new invoice with the same details
      const invoiceData: Omit<Invoice, "id" | "createdAt" | "updatedAt" | "amount"> = {
        title: selectedInvoice.title,
        customerId: selectedInvoice.customerId,
        projectId: selectedInvoice.projectId,
        quoteId: selectedInvoice.quoteId,
        poNumber: selectedInvoice.poNumber,
        // Use the original status for the form, but it will still be editable
        status: selectedInvoice.status as Invoice['status'],
        dueDate: new Date().toISOString().split('T')[0], // Set new due date
        items: selectedInvoice.items,
        parentInvoiceId: selectedInvoice.id // Link to the original invoice
      };

      // Create the new invoice as draft in the database
      const draftData = { ...invoiceData, status: 'draft' as Invoice['status'] };
      const newInvoice = await invoiceService.createInvoice(draftData);

      toast({
        title: "Invoice Revised",
        description: `The original invoice has been cancelled and a new draft invoice has been created for editing.`,
      });

      // Refresh invoices list
      const updatedInvoices = await invoiceService.getInvoices();
      setInvoices(updatedInvoices);

      // Close the revise dialog
      setIsReviseDialogOpen(false);

      // Open the edit dialog with the original status in the form
      // This allows editing but will show a warning when saving if status is not draft
      const editInvoice = { ...newInvoice, status: selectedInvoice.status };
      setSelectedInvoice(editInvoice);
      form.reset({
        customerId: editInvoice.customerId,
        projectId: editInvoice.projectId || "none",
        quoteId: editInvoice.quoteId || "none",
        poNumber: editInvoice.poNumber || "",
        title: editInvoice.title,
        status: editInvoice.status, // Use original status in the form
        dueDate: new Date(editInvoice.dueDate).toISOString().split('T')[0],
        items: editInvoice.items,
      });
      setIsAddDialogOpen(true);

    } catch (error) {
      console.error("Error revising invoice:", error);
      toast({
        title: "Error",
        description: "Failed to revise invoice. Please try again.",
        variant: "destructive",
      });
    }
  };

  const formatStatusTitle = (status: string) => {
    // Show "Due" for draft and sent statuses
    if (status === 'draft' || status === 'sent') {
      return 'Due';
    }
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  // Function to add a new invoice item
  const addInvoiceItem = () => {
    const items = form.getValues("items");
    form.setValue("items", [
      ...items,
      {
        description: "",
        quantity: 1,
        unitPrice: 0,
        total: 0,
      }
    ]);
  };

  // Function to remove an invoice item
  const removeInvoiceItem = (index: number) => {
    const items = form.getValues("items");
    if (items.length > 1) {
      form.setValue("items", items.filter((_, i) => i !== index));
    }
  };

  // Function to update the total when quantity or unit price changes
  const updateItemTotal = (index: number) => {
    const items = form.getValues("items");
    const item = items[index];
    const total = item.quantity * item.unitPrice;
    form.setValue(`items.${index}.total`, total);
  };

  // Calculate invoice total
  const calculateInvoiceTotal = (items: any[]) => {
    return items.reduce((sum, item) => sum + (item.total || 0), 0);
  };



  return (
    <CrmLayout
      title="Invoices"
      breadcrumbs={[{ title: "Invoices" }]}
    >
      <Card className="shadow-sm border-0">
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle className="font-dm-serif text-techlocal-dark">Invoice Management</CardTitle>
              <CardDescription>Manage your invoices and payments</CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="search"
                  placeholder="Search invoices..."
                  className="pl-8 w-full sm:w-[250px]"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select
                value={statusFilter}
                onValueChange={setStatusFilter}
              >
                <SelectTrigger className="w-full sm:w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="sent">Sent</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              <Button
                onClick={() => {
                  form.reset({
                    customerId: "",
                    projectId: "none",
                    quoteId: "none",
                    poNumber: "",
                    title: "New Invoice",
                    status: "draft",
                    dueDate: "",
                    items: [
                      {
                        description: "",
                        quantity: 1,
                        unitPrice: 0,
                        total: 0,
                      }
                    ],
                  });
                  setSelectedInvoice(null);
                  setIsAddDialogOpen(true);
                }}
                className="bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
              >
                <Plus className="mr-2 h-4 w-4" />
                Create Invoice
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Invoice Number</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      Loading invoices...
                    </TableCell>
                  </TableRow>
                ) : filteredInvoices.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      No invoices found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredInvoices.map((invoice) => (
                    <TableRow
                      key={invoice.id}
                      id={`invoice-row-${invoice.id}`}
                      className={`cursor-pointer hover:bg-gray-50 ${highlightedInvoiceId === invoice.id ? 'bg-yellow-50 border-l-4 border-yellow-400' : ''}`}
                      onClick={(e) => {
                        // Prevent row click if clicking on status tag or dropdown
                        if (
                          e.target instanceof HTMLElement &&
                          (e.target.closest('.status-tag') ||
                           e.target.closest('.dropdown-trigger') ||
                           e.target.closest('.customer-link'))
                        ) {
                          return;
                        }
                        if (invoice.status === 'draft') {
                          handleEditInvoice(invoice);
                        } else {
                          handlePreviewInvoice(invoice);
                        }
                      }}
                    >
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <span className="text-left hover:text-techlocal-dark">
                            {getInvoiceNumber(invoice.id)}
                          </span>
                          {invoice.parentInvoiceId && (
                            <span className="text-xs text-gray-500">(Revised)</span>
                          )}
                          {invoice.status === 'archived' && (
                            <span className="text-xs text-purple-600 font-medium">(Archived)</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewCustomer(invoice.customerId);
                          }}
                          className="text-left hover:text-techlocal-dark hover:underline customer-link"
                        >
                          {getCustomerName(invoice.customerId)}
                        </button>
                      </TableCell>
                      <TableCell>{formatCurrency(invoice.amount)}</TableCell>
                      <TableCell>
                        {invoice.status === 'archived' ? (
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium status-tag ${getStatusColor(invoice.status)}`}>
                            {formatStatusTitle(invoice.status)}
                          </span>
                        ) : (
                          <div onClick={(e) => e.stopPropagation()} className="status-tag">
                            <StatusChangeDropdown
                              currentStatus={invoice.status}
                              entityType="invoice"
                            onStatusChange={async (newStatus) => {
                              try {
                                // Check if we're changing from draft to something else and there's an associated quote
                                const isStatusChangingFromDraft = invoice.status === 'draft' && newStatus !== 'draft';
                                const hasAssociatedQuote = invoice.quoteId && invoice.quoteId !== 'none';

                                // Update the invoice status - cast to Invoice['status'] to fix type issue
                                await invoiceService.updateInvoice(invoice.id, { status: newStatus as Invoice['status'] });

                                // If status changed from draft to something else and there's an associated quote, update the quote status to accepted
                                if (isStatusChangingFromDraft && hasAssociatedQuote) {
                                  try {
                                    await quoteService.updateQuote(invoice.quoteId as string, { status: 'accepted' });
                                    toast({
                                      title: "Quote Status Updated",
                                      description: "The associated quote has been marked as accepted.",
                                    });

                                    // Refresh quotes list
                                    const updatedQuotes = await quoteService.getQuotes();
                                    setQuotes(updatedQuotes);
                                  } catch (quoteError) {
                                    console.error("Error updating quote status:", quoteError);
                                    toast({
                                      title: "Warning",
                                      description: "Invoice updated but failed to update the associated quote status.",
                                      variant: "destructive",
                                    });
                                  }
                                }

                                // Refresh invoice list
                                const updatedInvoices = await invoiceService.getInvoices();
                                setInvoices(updatedInvoices);

                                toast({
                                  title: "Status Updated",
                                  description: `Invoice status changed to ${formatStatusTitle(newStatus)}.`,
                                });
                              } catch (error) {
                                console.error("Error updating invoice status:", error);
                                toast({
                                  title: "Error",
                                  description: "Failed to update invoice status. Please try again.",
                                  variant: "destructive",
                                });
                              }
                            }}
                            />
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {new Date(invoice.dueDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              className="h-8 w-8 p-0 dropdown-trigger"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {invoice.status === 'draft' && (
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditInvoice(invoice);
                                }}
                              >
                              <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                            )}
                            {invoice.status !== 'draft' && invoice.status !== 'archived' && (
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleReviseInvoice(invoice);
                                }}
                              >
                                <Copy className="mr-2 h-4 w-4" />
                                Cancel & Revise
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem
                              onClick={(e) => {
                                // Stop event propagation to prevent the row click handler from firing
                                e.stopPropagation();
                                handleDeleteInvoice(invoice);
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handlePreviewInvoice(invoice);
                              }}
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              Preview
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownloadInvoice(invoice);
                              }}
                            >
                              <Download className="mr-2 h-4 w-4" />
                              Download PDF
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handlePreviewInvoice(invoice, "email");
                              }}
                            >
                              <Mail className="mr-2 h-4 w-4" />
                              Email to Customer
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDuplicateInvoice(invoice);
                              }}
                            >
                              <CopyPlus className="mr-2 h-4 w-4" />
                              Duplicate
                            </DropdownMenuItem>
                            {invoice.status !== 'paid' && invoice.status !== 'archived' && (
                              <DropdownMenuItem
                                onClick={async (e) => {
                                  e.stopPropagation();
                                  try {
                                    // Update the invoice status to paid
                                    await invoiceService.updateInvoice(invoice.id, { status: 'paid' });

                                    // Refresh invoice list
                                    const updatedInvoices = await invoiceService.getInvoices();
                                    setInvoices(updatedInvoices);

                                    toast({
                                      title: "Status Updated",
                                      description: "Invoice has been marked as paid.",
                                    });
                                  } catch (error) {
                                    console.error("Error updating invoice status:", error);
                                    toast({
                                      title: "Error",
                                      description: "Failed to mark invoice as paid. Please try again.",
                                      variant: "destructive",
                                    });
                                  }
                                }}
                              >
                                <CheckCircle2 className="mr-2 h-4 w-4 text-green-600" />
                                Mark as Paid
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add/Edit Invoice Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
        try {
          setIsAddDialogOpen(open);
          if (!open) {
            // Reset form when dialog is closed
            form.reset();
            setSelectedInvoice(null);
          }
        } catch (error) {
          console.error("Error toggling dialog:", error);
          toast({
            title: "Error",
            description: "There was an issue with the dialog. Please try again.",
            variant: "destructive",
          });
        }
      }}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="font-dm-serif text-techlocal-dark">
              {selectedInvoice ? "Edit Invoice" : "Create New Invoice"}
            </DialogTitle>
            <DialogDescription>
              {selectedInvoice
                ? "Update invoice information"
                : "Enter the details of the new invoice"}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddInvoice)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Invoice Title</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter invoice title"
                          onChange={(e) => {
                            field.onChange(e);
                            // If customer and quote/project are selected, auto-generate title
                            const customerId = form.getValues("customerId");
                            const quoteId = form.getValues("quoteId");
                            const projectId = form.getValues("projectId");
                            if (customerId && (e.target.value === "" || !field.value)) {
                              const title = getTitleFromRelatedEntity(customerId, quoteId, projectId);
                              field.onChange(title);
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="customerId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select customer" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {customers.map(customer => (
                            <SelectItem key={customer.id} value={customer.id}>
                              {customer.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="projectId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Related Project (Optional)</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select project" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          {projects
                            .filter(project => form.getValues("customerId") ? project.customerId === form.getValues("customerId") : true)
                            .map(project => (
                              <SelectItem key={project.id} value={project.id}>
                                {project.title}
                              </SelectItem>
                            ))
                          }
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="quoteId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Based on Quote (Optional)</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          // Check if a quote is being selected (not 'none')
                          const previousValue = field.value;
                          field.onChange(value);

                          if (value !== 'none' && value !== previousValue) {
                            handleQuoteSelection(value);
                          }
                        }}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select quote" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          {quotes
                            .filter(quote => form.getValues("customerId") ? quote.customerId === form.getValues("customerId") : true)
                            .filter(quote => quote.status !== 'draft' && quote.status !== 'rejected' && quote.status !== 'expired' && quote.status !== 'archived')
                            .map(quote => (
                              <SelectItem key={quote.id} value={quote.id}>
                                {quote.title}
                              </SelectItem>
                            ))
                          }

                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="poNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>PO Number (Optional)</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter PO number" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="sent">Sent</SelectItem>
                          <SelectItem value="paid">Paid</SelectItem>
                          <SelectItem value="overdue">Overdue</SelectItem>
                          <SelectItem value="cancelled">Cancelled</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="dueDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Due Date</FormLabel>
                      <FormControl>
                        <Input {...field} type="date" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium">Invoice Items</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addInvoiceItem}
                  >
                    <Plus className="mr-1 h-3 w-3" />
                    Add Item
                  </Button>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[40%]">Description</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Unit Price</TableHead>
                        <TableHead>Total</TableHead>
                        <TableHead className="w-[50px]"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {form.watch("items").map((_, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <FormField
                              control={form.control}
                              name={`items.${index}.description`}
                              render={({ field }) => (
                                <FormItem className="m-0">
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder="Item description"
                                      className="border-0 p-0 h-8 focus-visible:ring-0"
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <FormField
                              control={form.control}
                              name={`items.${index}.quantity`}
                              render={({ field }) => (
                                <FormItem className="m-0">
                                  <FormControl>
                                    <Input
                                      {...field}
                                      type="number"
                                      min="1"
                                      className="border-0 p-0 h-8 focus-visible:ring-0 w-16"
                                      onChange={(e) => {
                                        field.onChange(e);
                                        updateItemTotal(index);
                                      }}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <FormField
                              control={form.control}
                              name={`items.${index}.unitPrice`}
                              render={({ field }) => (
                                <FormItem className="m-0">
                                  <FormControl>
                                    <Input
                                      {...field}
                                      type="number"
                                      min="0"
                                      className="border-0 p-0 h-8 focus-visible:ring-0 w-24"
                                      onChange={(e) => {
                                        field.onChange(e);
                                        updateItemTotal(index);
                                      }}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <FormField
                              control={form.control}
                              name={`items.${index}.total`}
                              render={({ field }) => (
                                <FormItem className="m-0">
                                  <FormControl>
                                    <Input
                                      {...field}
                                      type="number"
                                      disabled
                                      className="border-0 p-0 h-8 focus-visible:ring-0 w-24 bg-transparent"
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => removeInvoiceItem(index)}
                              disabled={form.watch("items").length <= 1}
                            >
                              <Trash2 className="h-4 w-4 text-gray-500" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-end pt-2">
                  <div className="w-[200px] space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>Subtotal:</span>
                      <span>{formatCurrency(calculateInvoiceTotal(form.watch("items")))}</span>
                    </div>
                    <div className="flex justify-between text-sm font-bold">
                      <span>Total:</span>
                      <span>{formatCurrency(calculateInvoiceTotal(form.watch("items")))}</span>
                    </div>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <div className="flex flex-row gap-2 w-full justify-end">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsAddDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="outline"
                  >
                    {selectedInvoice ? "Update & Close" : "Save & Close"}
                  </Button>
                  <Button
                    type="button"
                    className="bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
                    onClick={async () => {
                      try {
                        const formData = form.getValues();

                        // Validate form data
                        if (!formData.customerId) {
                          toast({
                            title: "Missing Information",
                            description: "Please select a customer before generating.",
                            variant: "destructive",
                          });
                          return;
                        }

                        if (formData.items.length === 0 || !formData.items[0].description) {
                          toast({
                            title: "Missing Information",
                            description: "Please add at least one item before generating.",
                            variant: "destructive",
                          });
                          return;
                        }

                        // Check if this is a revised invoice with non-draft status
                        if (formData.status !== 'draft' && selectedInvoice?.parentInvoiceId) {
                          // This is a revised invoice being saved with a non-draft status
                          const confirmNonDraft = window.confirm(
                            "You are generating this invoice with a non-draft status. Once saved, you won't be able to edit it further. Continue?"
                          );

                          if (!confirmNonDraft) {
                            return; // User cancelled the operation
                          }
                        }

                        const invoiceData: any = {
                          title: formData.title,
                          customerId: formData.customerId,
                          poNumber: formData.poNumber || undefined,
                          status: formData.status,
                          dueDate: formData.dueDate,
                          projectId: formData.projectId === 'none' ? undefined : formData.projectId,
                          quoteId: formData.quoteId === 'none' ? undefined : formData.quoteId,
                          items: formData.items.map(item => ({
                            description: item.description,
                            quantity: item.quantity,
                            unitPrice: item.unitPrice,
                            total: item.quantity * item.unitPrice
                          }))
                        };

                        if (selectedInvoice) {
                          // Update existing invoice
                          await invoiceService.updateInvoice(selectedInvoice.id, invoiceData);

                          // Refresh the invoice list
                          const updatedInvoices = await invoiceService.getInvoices();
                          setInvoices(updatedInvoices);

                          // Find the updated invoice
                          const updatedInvoice = updatedInvoices.find(inv => inv.id === selectedInvoice.id);
                          if (updatedInvoice) {
                            setSelectedInvoice(updatedInvoice);
                          }

                          toast({
                            title: "Changes Saved",
                            description: "Invoice has been updated and will be previewed.",
                          });
                        } else {
                          // Create new invoice
                          const newInvoice = await invoiceService.createInvoice(invoiceData);

                          // Refresh the invoice list
                          const updatedInvoices = await invoiceService.getInvoices();
                          setInvoices(updatedInvoices);

                          // Set the new invoice as selected
                          setSelectedInvoice(newInvoice);

                          toast({
                            title: "Invoice Created",
                            description: "New invoice has been created and will be previewed.",
                          });
                        }

                        // Get the customer and open the preview dialog
                        const customer = customers.find(c => c.id === formData.customerId);
                        if (customer) {
                          setSelectedCustomer(customer);
                          setIsPreviewDialogOpen(true);
                          setIsAddDialogOpen(false); // Close the edit dialog
                        }
                      } catch (error) {
                        console.error("Error with invoice:", error);
                        toast({
                          title: "Error",
                          description: "Failed to process invoice. Please try again.",
                          variant: "destructive",
                        });
                      }
                    }}

                  >
                    <Eye className="mr-2 h-4 w-4" />
                    Generate
                  </Button>
                </div>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Invoice Preview Dialog */}
      {selectedInvoice && selectedCustomer && (
        <InvoicePreviewDialog
          open={isPreviewDialogOpen}
          onOpenChange={setIsPreviewDialogOpen}
          invoice={selectedInvoice}
          customer={selectedCustomer}
          initialTab={activePreviewTab}
          onInvoiceUpdated={() => {
            // Refresh invoices list when an invoice is updated (e.g., status changed to sent)
            invoiceService.getInvoices().then(updatedInvoices => {
              setInvoices(updatedInvoices);
            });
          }}
        />
      )}

      {/* Revise Invoice Confirmation Dialog */}
      <Dialog open={isReviseDialogOpen} onOpenChange={setIsReviseDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="font-dm-serif text-techlocal-dark flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Cancel & Revise Invoice
            </DialogTitle>
            <DialogDescription>
              <p className="mb-2">You are about to create a revised version of this invoice.</p>
              <p className="mb-2">The original invoice will be <strong>cancelled</strong> and a new draft invoice will be created.</p>
              <p>This ensures that invoices remain immutable once they've been sent to customers.</p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4">
            <Button
              variant="outline"
              onClick={() => setIsReviseDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmReviseInvoice}
            >
              Cancel & Create New Invoice
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Copy Line Items Dialog */}
      {selectedQuoteForCopy && (
        <CopyLineItemsDialog
          open={isCopyLineItemsDialogOpen}
          onOpenChange={setIsCopyLineItemsDialogOpen}
          sourceType="quote"
          sourceName={selectedQuoteForCopy.title}
          hasExistingItems={form.getValues('items').length > 0 && !!form.getValues('items')[0].description}
          onAction={(action: LineItemAction) => {
            // Handle the selected action
            if (selectedQuoteForCopy && selectedQuoteForCopy.items) {
              // Map quote items to invoice items
              const quoteItems = selectedQuoteForCopy.items.map(item => ({
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                total: item.quantity * item.unitPrice
              }));

              // Get current items
              const currentItems = form.getValues('items');

              // Update the form based on the selected action
              switch (action) {
                case 'overwrite':
                  form.setValue('items', quoteItems);
                  toast({
                    title: "Line Items Replaced",
                    description: `${quoteItems.length} items from the quote have replaced the existing items.`,
                  });
                  break;

                case 'add':
                  // Filter out empty items from current items
                  const validCurrentItems = currentItems.filter(item =>
                    item.description && item.description.trim() !== ''
                  );

                  // Combine current items with quote items
                  form.setValue('items', [...validCurrentItems, ...quoteItems]);
                  toast({
                    title: "Line Items Added",
                    description: `${quoteItems.length} items from the quote have been added to your existing items.`,
                  });
                  break;

                case 'keep':
                  // Do nothing, keep current items
                  toast({
                    title: "No Changes Made",
                    description: "Your existing line items have been kept unchanged.",
                  });
                  break;
              }

              // Update the title if it's empty or default, regardless of the line item action
              const currentTitle = form.getValues('title');
              if (!currentTitle || currentTitle === 'New Invoice') {
                form.setValue('title', `Invoice for ${selectedQuoteForCopy.title}`);
              }
            }
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={(open) => {
        try {
          setIsDeleteDialogOpen(open);
          if (!open) {
            // Reset selected invoice when dialog is closed
            setSelectedInvoice(null);
          }
        } catch (error) {
          console.error("Error toggling delete dialog:", error);
          toast({
            title: "Error",
            description: "There was an issue with the dialog. Please try again.",
            variant: "destructive",
          });
        }
      }}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="font-dm-serif text-techlocal-dark">Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete Invoice {selectedInvoice ? getInvoiceNumber(selectedInvoice.id) : ''}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>


    </CrmLayout>
  );
};

export default Invoices;
