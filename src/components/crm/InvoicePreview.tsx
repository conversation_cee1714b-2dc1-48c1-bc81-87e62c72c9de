import React from "react";
import { Invoice, Customer, getInvoiceNumber } from "@/types/crm";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";

interface InvoicePreviewProps {
  invoice: Invoice;
  customer: Customer;
  companyInfo: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website: string;
  };
}

const InvoicePreview = React.forwardRef<HTMLDivElement, InvoicePreviewProps>(
  ({ invoice, customer, companyInfo }, ref) => {
    return (
      <div ref={ref} className="bg-white p-8 max-w-4xl mx-auto">
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">INVOICE</h1>
            <p className="text-gray-600 mt-1">#{getInvoiceNumber(invoice.id)}</p>
          </div>
          <div className="text-right">
            <h2 className="text-xl font-bold text-gray-900">{companyInfo.name}</h2>
            <p className="text-gray-600 whitespace-pre-line">{companyInfo.address}</p>
            <p className="text-gray-600">{companyInfo.phone}</p>
            <p className="text-gray-600">{companyInfo.email}</p>
            <p className="text-gray-600">{companyInfo.website}</p>
          </div>
        </div>

        <div className="flex justify-between mb-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Bill To:</h3>
            <p className="font-semibold">{customer.name}</p>
            <p>{customer.contactName}</p>
            <p>{customer.email}</p>
            <p>{customer.contactNumber}</p>
            {customer.address && <p>{customer.address}</p>}
          </div>
          <div className="text-right">
            <div className="mb-2">
              <span className="font-semibold mr-2">Invoice Date:</span>
              <span>{format(new Date(invoice.createdAt), "MMMM dd, yyyy")}</span>
            </div>
            {invoice.poNumber && (
              <div className="mb-2">
                <span className="font-semibold mr-2">PO Number:</span>
                <span>{invoice.poNumber}</span>
              </div>
            )}
            <div className="mb-2">
              <span className="font-semibold mr-2">Due Date:</span>
              <span>{format(new Date(invoice.dueDate), "MMMM dd, yyyy")}</span>
            </div>
            <div>
              <span className="font-semibold mr-2">Status:</span>
              <span className="capitalize">
                {invoice.status === 'draft' || invoice.status === 'sent'
                  ? 'Due'
                  : invoice.status}
              </span>
            </div>
          </div>
        </div>

        <div className="mb-8">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-100 text-left">
                <th className="py-2 px-4 border-b border-gray-200 w-[55%]">Description</th>
                <th className="py-2 px-4 border-b border-gray-200 text-right w-[15%]">Qty</th>
                <th className="py-2 px-4 border-b border-gray-200 text-right w-[15%]">Unit Price</th>
                <th className="py-2 px-4 border-b border-gray-200 text-right w-[15%]">Total</th>
              </tr>
            </thead>
            <tbody>
              {invoice.items.map((item, index) => (
                <tr key={index} className="border-b border-gray-200">
                  <td className="py-2 px-4">{item.description}</td>
                  <td className="py-2 px-4 text-right">{item.quantity}</td>
                  <td className="py-2 px-4 text-right">{formatCurrency(item.unitPrice)}</td>
                  <td className="py-2 px-4 text-right">{formatCurrency(item.total)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="flex justify-end mb-8">
          <div className="w-1/3">
            <div className="flex justify-between mb-2">
              <span className="font-semibold">Subtotal:</span>
              <span>{formatCurrency(invoice.amount)}</span>
            </div>
            <div className="flex justify-between font-bold text-lg">
              <span>Total:</span>
              <span>{formatCurrency(invoice.amount)}</span>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-200 pt-6 mt-8">
          <h3 className="font-semibold mb-2">Payment Terms</h3>
          <p>Please make payment by the due date.</p>

          <h3 className="font-semibold mb-2 mt-4">Banking Details</h3>
          <p>Bank: Capitec Business</p>
          <p>Account Name: Tech Local</p>
          <p>Account Type: Transact</p>
          <p>Account Number: **********</p>
          <p>Branch Code: 450105</p>

          <p className="mt-4">Thank you for your business!</p>
        </div>
      </div>
    );
  }
);

InvoicePreview.displayName = "InvoicePreview";

export default InvoicePreview;
