
import {
  Document,
  Page,
  View,
  Text,
} from '@react-pdf/renderer';
import { Invoice, Customer, getInvoiceNumber, getRemainingAmount } from '@/types/crm';
import { format } from 'date-fns';
import { formatCurrency } from '@/lib/utils';
import { styles, HeaderCell, Cell } from './PdfStyles';

type CompanyInfo = {
  name: string;
  address: string;
  phone: string;
  email: string;
  website: string;
};

interface InvoicePdfDocumentProps {
  invoice: Invoice;
  customer: Customer;
  companyInfo: CompanyInfo;
}

export default function InvoicePdfDocument({
  invoice,
  customer,
  companyInfo,
}: InvoicePdfDocumentProps) {
  const issued = format(new Date(invoice.createdAt), 'MMMM dd, yyyy');
  const dueDate = format(new Date(invoice.dueDate), 'MMMM dd, yyyy');

  return (
    <Document>
      <Page size="A4" style={styles.page} wrap>
        {/* ------------ header ------------------------------------------------ */}
        <View style={styles.row}>
          <View>
            <Text style={styles.h1}>INVOICE</Text>
            <Text style={{ marginTop: 2 }}># {getInvoiceNumber(invoice.id)}</Text>
          </View>

          <View style={styles.companyInfo}>
            <Text style={[styles.bold, styles.right]}>{companyInfo.name}</Text>
            <Text style={styles.right}>{companyInfo.address}</Text>
            <Text style={styles.right}>{companyInfo.phone}</Text>
            <Text style={styles.right}>{companyInfo.email}</Text>
            <Text style={styles.right}>{companyInfo.website}</Text>
          </View>
        </View>

        {/* ------------ customer & meta -------------------------------------- */}
        <View style={[styles.row, { marginTop: 24 }]}>
          <View style={{ maxWidth: '55%' }}>
            <Text style={styles.h2}>Bill To:</Text>
            <Text style={styles.bold}>{customer.name}</Text>
            {customer.contactName && <Text>{customer.contactName}</Text>}
            {customer.email && <Text>{customer.email}</Text>}
            {customer.contactNumber && <Text>{customer.contactNumber}</Text>}
            {customer.address && <Text>{customer.address}</Text>}
          </View>

          <View style={styles.right}>
            <Text>
              <Text style={styles.bold}>Invoice Date: </Text>{issued}
            </Text>
            {invoice.poNumber && (
              <Text>
                <Text style={styles.bold}>PO Number: </Text>{invoice.poNumber}
              </Text>
            )}
            <Text>
              <Text style={styles.bold}>Due Date: </Text>{dueDate}
            </Text>
            <Text>
              <Text style={styles.bold}>Status: </Text>
              {invoice.status === 'draft' || invoice.status === 'sent'
                ? 'Due'
                : invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
            </Text>
          </View>
        </View>

        {/* ------------ items table ------------------------------------------ */}
        <View style={{ marginTop: 24 }}>
          {/* header row */}
          <View style={styles.tableHead}>
            <HeaderCell idx={0}>Description</HeaderCell>
            <HeaderCell idx={1} align="right">Qty</HeaderCell>
            <HeaderCell idx={2} align="right">Unit Price</HeaderCell>
            <HeaderCell idx={3} align="right">Total</HeaderCell>
          </View>

          {/* body rows */}
          {invoice.items.map((item, idx) => (
            <View key={idx} style={{ flexDirection: 'row' }}>
              <Cell idx={0}>{item.description}</Cell>
              <Cell idx={1} align="right">{item.quantity}</Cell>
              <Cell idx={2} align="right">{formatCurrency(item.unitPrice)}</Cell>
              <Cell idx={3} align="right">{formatCurrency(item.total)}</Cell>
            </View>
          ))}
        </View>

        {/* ------------ totals ----------------------------------------------- */}
        <View style={styles.totals}>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Subtotal:</Text>
            <Text style={[styles.bold, styles.totalValue]}>{formatCurrency(invoice.amount)}</Text>
          </View>
          {invoice.isPartial && invoice.partialAmount && (
            <View style={styles.totalRow}>
              <Text style={[styles.totalLabel, { color: '#2563eb' }]}>Invoice Amount:</Text>
              <Text style={[styles.bold, styles.totalValue, { color: '#2563eb' }]}>{formatCurrency(invoice.partialAmount)}</Text>
            </View>
          )}
          <View style={[styles.totalRow, { borderTop: '1pt solid #000', paddingTop: 4 }]}>
            <Text style={[styles.totalLabel, styles.bold]}>Total Due:</Text>
            <Text style={[styles.bold, styles.totalValue]}>{formatCurrency(invoice.isPartial && invoice.partialAmount ? invoice.partialAmount : invoice.amount)}</Text>
          </View>
          {invoice.amountPaid > 0 && (
            <>
              <View style={styles.totalRow}>
                <Text style={[styles.totalLabel, { color: '#16a34a' }]}>Amount Paid:</Text>
                <Text style={[styles.bold, styles.totalValue, { color: '#16a34a' }]}>{formatCurrency(invoice.amountPaid)}</Text>
              </View>
              <View style={[styles.totalRow, { borderTop: '1pt solid #000', paddingTop: 4 }]}>
                <Text style={[styles.totalLabel, styles.bold]}>Remaining Balance:</Text>
                <Text style={[styles.bold, styles.totalValue, { color: getRemainingAmount(invoice) > 0 ? '#ea580c' : '#16a34a' }]}>
                  {formatCurrency(getRemainingAmount(invoice))}
                </Text>
              </View>
            </>
          )}
        </View>

        {/* ------------ notes & footer --------------------------------------- */}
        <View style={styles.notes}>
          <Text style={styles.h2}>Payment Terms:</Text>
          <Text>Please make payment by the due date.</Text>

          <Text style={[styles.h2, { marginTop: 10 }]}>Banking Details:</Text>
          <Text>Bank: Capitec Business</Text>
          <Text>Account Name: Tech Local</Text>
          <Text>Account Type: Transact</Text>
          <Text>Account Number: **********</Text>
          <Text>Branch Code: 450105</Text>
        </View>

        <Text style={styles.footer}>Thank you for your business!</Text>
      </Page>
    </Document>
  );
}
