import { supabase } from "@/integrations/supabase/client";
import { Invoice, InvoiceItem } from "@/types/crm";

export const invoiceService = {
  // Get all invoices
  async getInvoices(): Promise<Invoice[]> {
    const { data, error } = await supabase
      .from("invoices")
      .select("*, invoice_items(*)")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching invoices:", error);
      throw error;
    }

    return data.map((invoice) => ({
      id: invoice.id,
      customerId: invoice.customer_id,
      projectId: invoice.project_id,
      quoteId: invoice.quote_id,
      poNumber: invoice.po_number,
      title: invoice.title || 'Untitled Invoice', // Fallback for existing invoices
      amount: invoice.amount,
      status: invoice.status,
      dueDate: invoice.due_date,
      createdAt: invoice.created_at,
      updatedAt: invoice.updated_at,
      parentInvoiceId: invoice.parent_invoice_id,
      items: invoice.invoice_items.map((item: any) => ({
        id: item.id,
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unit_price,
        total: item.total,
      })),
    }));
  },

  // Get invoice by ID
  async getInvoiceById(id: string): Promise<Invoice> {
    const { data, error } = await supabase
      .from("invoices")
      .select("*, invoice_items(*)")
      .eq("id", id)
      .single();

    if (error) {
      console.error(`Error fetching invoice with ID ${id}:`, error);
      throw error;
    }

    return {
      id: data.id,
      customerId: data.customer_id,
      projectId: data.project_id,
      quoteId: data.quote_id,
      poNumber: data.po_number,
      title: data.title || 'Untitled Invoice', // Fallback for existing invoices
      amount: data.amount,
      partialAmount: data.partial_amount,
      amountPaid: data.amount_paid || 0,
      isPartial: data.is_partial || false,
      status: data.status,
      dueDate: data.due_date,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      parentInvoiceId: data.parent_invoice_id,
      items: data.invoice_items.map((item: any) => ({
        id: item.id,
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unit_price,
        total: item.total,
      })),
    };
  },

  // Get invoices by customer ID
  async getInvoicesByCustomerId(customerId: string): Promise<Invoice[]> {
    const { data, error } = await supabase
      .from("invoices")
      .select("*, invoice_items(*)")
      .eq("customer_id", customerId)
      .order("created_at", { ascending: false });

    if (error) {
      console.error(`Error fetching invoices for customer ${customerId}:`, error);
      throw error;
    }

    return data.map((invoice) => ({
      id: invoice.id,
      customerId: invoice.customer_id,
      projectId: invoice.project_id,
      quoteId: invoice.quote_id,
      poNumber: invoice.po_number,
      title: invoice.title || 'Untitled Invoice', // Fallback for existing invoices
      amount: invoice.amount,
      status: invoice.status,
      dueDate: invoice.due_date,
      createdAt: invoice.created_at,
      updatedAt: invoice.updated_at,
      parentInvoiceId: invoice.parent_invoice_id,
      items: invoice.invoice_items.map((item: any) => ({
        id: item.id,
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unit_price,
        total: item.total,
      })),
    }));
  },

  // Create a new invoice
  async createInvoice(invoice: Omit<Invoice, "id" | "createdAt" | "updatedAt" | "amount">): Promise<Invoice> {
    // Calculate total amount from items
    const totalAmount = invoice.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);

    // Use partial amount if specified and is_partial is true, otherwise use total
    const invoiceAmount = invoice.isPartial && invoice.partialAmount !== undefined
      ? invoice.partialAmount
      : totalAmount;

    // Start a transaction
    const { data, error } = await supabase.rpc('create_invoice', {
      p_customer_id: invoice.customerId,
      p_project_id: invoice.projectId,
      p_quote_id: invoice.quoteId,
      p_title: invoice.title,
      p_status: invoice.status,
      p_due_date: invoice.dueDate,
      p_po_number: invoice.poNumber,
      p_parent_invoice_id: invoice.parentInvoiceId,
      p_partial_amount: invoice.partialAmount || null,
      p_amount_paid: invoice.amountPaid || 0,
      p_is_partial: invoice.isPartial || false,
      p_items: invoice.items.map(item => ({
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unitPrice,
        total: item.quantity * item.unitPrice
      }))
    });

    if (error) {
      console.error("Error creating invoice:", error);
      throw error;
    }

    // Fetch the created invoice with its items
    return await this.getInvoiceById(data.id);
  },

  // Update an existing invoice
  async updateInvoice(id: string, invoice: Partial<Omit<Invoice, "id" | "createdAt" | "updatedAt" | "amount">>): Promise<Invoice> {
    const updateData: any = {};

    if (invoice.customerId !== undefined) updateData.customer_id = invoice.customerId;
    if (invoice.projectId !== undefined) updateData.project_id = invoice.projectId;
    if (invoice.quoteId !== undefined) updateData.quote_id = invoice.quoteId;
    if (invoice.title !== undefined) updateData.title = invoice.title;
    if (invoice.status !== undefined) updateData.status = invoice.status;
    if (invoice.dueDate !== undefined) updateData.due_date = invoice.dueDate;
    if (invoice.poNumber !== undefined) updateData.po_number = invoice.poNumber;
    if (invoice.parentInvoiceId !== undefined) updateData.parent_invoice_id = invoice.parentInvoiceId === null ? null : invoice.parentInvoiceId;
    if (invoice.partialAmount !== undefined) updateData.partial_amount = invoice.partialAmount;
    if (invoice.amountPaid !== undefined) updateData.amount_paid = invoice.amountPaid;
    if (invoice.isPartial !== undefined) updateData.is_partial = invoice.isPartial;

    // Update invoice
    const { error: invoiceError } = await supabase
      .from("invoices")
      .update(updateData)
      .eq("id", id);

    if (invoiceError) {
      console.error(`Error updating invoice with ID ${id}:`, invoiceError);
      throw invoiceError;
    }

    // Update invoice items if provided
    if (invoice.items && invoice.items.length > 0) {
      // Delete existing items
      const { error: deleteError } = await supabase
        .from("invoice_items")
        .delete()
        .eq("invoice_id", id);

      if (deleteError) {
        console.error(`Error deleting invoice items for invoice ${id}:`, deleteError);
        throw deleteError;
      }

      // Insert new items
      const { error: insertError } = await supabase
        .from("invoice_items")
        .insert(
          invoice.items.map(item => ({
            invoice_id: id,
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unitPrice,
            total: item.quantity * item.unitPrice
          }))
        );

      if (insertError) {
        console.error(`Error inserting invoice items for invoice ${id}:`, insertError);
        throw insertError;
      }
    }

    // Fetch the updated invoice with its items
    return await this.getInvoiceById(id);
  },

  // Create a new invoice as a revision of an existing invoice
  async createInvoiceRevision(originalInvoiceId: string, newStatus: Invoice['status'] = 'draft'): Promise<Invoice> {
    // Get the original invoice
    const originalInvoice = await this.getInvoiceById(originalInvoiceId);

    // Archive the original invoice
    await this.archiveInvoice(originalInvoiceId);

    // Create a new invoice based on the original
    const newInvoiceData = {
      title: `${originalInvoice.title} (Revised)`,
      customerId: originalInvoice.customerId,
      projectId: originalInvoice.projectId || null,
      quoteId: originalInvoice.quoteId || null,
      invoiceNumber: this.generateInvoiceNumber(originalInvoice.id),
      poNumber: originalInvoice.poNumber,
      status: newStatus,
      dueDate: originalInvoice.dueDate,
      parentInvoiceId: originalInvoiceId,
      items: originalInvoice.items.map(item => ({
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.quantity * item.unitPrice
      }))
    };

    return await this.createInvoice(newInvoiceData);
  },

  // Archive an invoice (mark as archived without deleting)
  async archiveInvoice(id: string): Promise<Invoice> {
    const { error } = await supabase
      .from("invoices")
      .update({ status: 'archived' })
      .eq("id", id);

    if (error) {
      console.error(`Error archiving invoice with ID ${id}:`, error);
      throw error;
    }

    return await this.getInvoiceById(id);
  },



  // Delete an invoice
  async deleteInvoice(id: string): Promise<void> {
    // Delete invoice items first (should cascade, but just to be safe)
    const { error: itemsError } = await supabase
      .from("invoice_items")
      .delete()
      .eq("invoice_id", id);

    if (itemsError) {
      console.error(`Error deleting invoice items for invoice ${id}:`, itemsError);
      throw itemsError;
    }

    // Delete the invoice
    const { error } = await supabase
      .from("invoices")
      .delete()
      .eq("id", id);

    if (error) {
      console.error(`Error deleting invoice with ID ${id}:`, error);
      throw error;
    }
  },

  // Add an item to an invoice
  async addInvoiceItem(invoiceId: string, item: Omit<InvoiceItem, "id">): Promise<InvoiceItem> {
    const { data, error } = await supabase
      .from("invoice_items")
      .insert({
        invoice_id: invoiceId,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unitPrice,
        total: item.total,
      })
      .select()
      .single();

    if (error) {
      console.error(`Error adding item to invoice ${invoiceId}:`, error);
      throw error;
    }

    return {
      id: data.id,
      description: data.description,
      quantity: data.quantity,
      unitPrice: data.unit_price,
      total: data.total,
    };
  },

  // Update an invoice item
  async updateInvoiceItem(id: string, item: Partial<Omit<InvoiceItem, "id">>): Promise<InvoiceItem> {
    const updateData: any = {};

    if (item.description !== undefined) updateData.description = item.description;
    if (item.quantity !== undefined) updateData.quantity = item.quantity;
    if (item.unitPrice !== undefined) updateData.unit_price = item.unitPrice;
    if (item.total !== undefined) updateData.total = item.total;

    const { data, error } = await supabase
      .from("invoice_items")
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating invoice item with ID ${id}:`, error);
      throw error;
    }

    return {
      id: data.id,
      description: data.description,
      quantity: data.quantity,
      unitPrice: data.unit_price,
      total: data.total,
    };
  },

  // Delete an invoice item
  async deleteInvoiceItem(id: string): Promise<void> {
    const { error } = await supabase
      .from("invoice_items")
      .delete()
      .eq("id", id);

    if (error) {
      console.error(`Error deleting invoice item with ID ${id}:`, error);
      throw error;
    }
  },

  // Update payment amount for an invoice
  async updatePaymentAmount(id: string, amountPaid: number): Promise<Invoice> {
    const { error } = await supabase
      .from("invoices")
      .update({
        amount_paid: amountPaid,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id);

    if (error) {
      console.error(`Error updating payment amount for invoice ${id}:`, error);
      throw error;
    }

    return await this.getInvoiceById(id);
  },

  // Get total invoiced amount
  async getTotalInvoicedAmount(): Promise<number> {
    const { data, error } = await supabase
      .from("invoices")
      .select("amount");

    if (error) {
      console.error("Error fetching total invoiced amount:", error);
      throw error;
    }

    return data.reduce((total, invoice) => total + (invoice.amount || 0), 0);
  },

  // Get total paid amount
  async getTotalPaidAmount(): Promise<number> {
    const { data, error } = await supabase
      .from("invoices")
      .select("amount")
      .eq("status", "paid");

    if (error) {
      console.error("Error fetching total paid amount:", error);
      throw error;
    }

    return data.reduce((total, invoice) => total + (invoice.amount || 0), 0);
  },

  // Get total outstanding amount
  async getTotalOutstandingAmount(): Promise<number> {
    const { data, error } = await supabase
      .from("invoices")
      .select("amount")
      .in("status", ["sent", "overdue"]);

    if (error) {
      console.error("Error fetching total outstanding amount:", error);
      throw error;
    }

    return data.reduce((total, invoice) => total + (invoice.amount || 0), 0);
  },
};
